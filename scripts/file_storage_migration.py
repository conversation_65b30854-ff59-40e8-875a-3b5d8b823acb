"""
文件存储迁移脚本
将数据库中的文件内容迁移到文件系统
"""

import asyncio
import os
import hashlib
from pathlib import Path
from datetime import datetime
from tortoise import Tortoise

from app.settings.config import APP_SETTINGS
from app.models.strm.upload import UploadRecord
from app.log.log import log


class FileStorageMigration:
    """文件存储迁移类"""
    
    def __init__(self):
        self.storage_dir = Path("storage/uploads")
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.migrated_count = 0
        self.error_count = 0
    
    async def migrate_upload_records(self):
        """
        迁移上传记录中的文件内容
        """
        log.info("开始迁移上传记录中的文件内容...")
        
        # 获取所有有文件内容的记录
        records = await UploadRecord.filter(file_content__not_isnull=True).all()
        total_records = len(records)
        
        log.info(f"找到 {total_records} 个需要迁移的记录")
        
        for i, record in enumerate(records, 1):
            try:
                await self._migrate_single_record(record)
                self.migrated_count += 1
                
                if i % 10 == 0:
                    log.info(f"迁移进度: {i}/{total_records} ({i/total_records*100:.1f}%)")
                    
            except Exception as e:
                log.error(f"迁移记录 {record.id} 失败: {str(e)}")
                self.error_count += 1
        
        log.info(f"迁移完成: 成功 {self.migrated_count} 个, 失败 {self.error_count} 个")
    
    async def _migrate_single_record(self, record: UploadRecord):
        """
        迁移单个记录
        """
        if not record.file_content:
            return
        
        # 生成文件路径
        file_path = self._generate_file_path(record)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(record.file_content)
        
        # 验证文件
        if not file_path.exists() or file_path.stat().st_size != len(record.file_content):
            raise Exception("文件写入验证失败")
        
        # 更新数据库记录
        record.file_path = str(file_path.relative_to(Path.cwd()))
        record.file_content = None  # 清空文件内容
        await record.save()
        
        log.debug(f"成功迁移记录 {record.id}: {record.filename}")
    
    def _generate_file_path(self, record: UploadRecord) -> Path:
        """
        生成文件存储路径
        """
        # 使用日期和ID组织目录结构
        date_str = record.create_time.strftime("%Y/%m/%d")
        dir_path = self.storage_dir / date_str
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名（避免重复）
        file_extension = Path(record.filename).suffix
        file_hash = hashlib.md5(f"{record.id}_{record.filename}".encode()).hexdigest()[:8]
        filename = f"{record.id}_{file_hash}{file_extension}"
        
        return dir_path / filename
    
    async def verify_migration(self):
        """
        验证迁移结果
        """
        log.info("开始验证迁移结果...")
        
        # 检查是否还有文件内容在数据库中
        records_with_content = await UploadRecord.filter(file_content__not_isnull=True).count()
        if records_with_content > 0:
            log.warning(f"仍有 {records_with_content} 个记录包含文件内容")
        
        # 检查文件路径记录
        records_with_path = await UploadRecord.filter(file_path__not_isnull=True).count()
        log.info(f"有 {records_with_path} 个记录包含文件路径")
        
        # 验证文件是否存在
        missing_files = 0
        records = await UploadRecord.filter(file_path__not_isnull=True).all()
        
        for record in records:
            if record.file_path and not Path(record.file_path).exists():
                log.warning(f"文件不存在: {record.file_path} (记录ID: {record.id})")
                missing_files += 1
        
        if missing_files == 0:
            log.info("✅ 所有文件验证通过")
        else:
            log.warning(f"⚠️ 有 {missing_files} 个文件缺失")
    
    async def rollback_migration(self):
        """
        回滚迁移（仅用于测试）
        """
        log.warning("开始回滚迁移...")
        
        records = await UploadRecord.filter(file_path__not_isnull=True).all()
        
        for record in records:
            try:
                if record.file_path and Path(record.file_path).exists():
                    # 读取文件内容
                    with open(record.file_path, 'rb') as f:
                        file_content = f.read()
                    
                    # 恢复到数据库
                    record.file_content = file_content
                    record.file_path = None
                    await record.save()
                    
                    # 删除文件
                    Path(record.file_path).unlink()
                    
                    log.debug(f"回滚记录 {record.id}")
                    
            except Exception as e:
                log.error(f"回滚记录 {record.id} 失败: {str(e)}")
        
        log.info("回滚完成")


async def create_optimized_upload_model():
    """
    创建优化的上传模型（用于参考）
    """
    model_code = '''
class OptimizedUploadRecord(BaseModel, TimestampMixin):
    """优化的文件上传记录"""
    id = fields.IntField(pk=True, description="记录ID")
    filename = fields.CharField(max_length=255, description="原始文件名")
    file_path = fields.CharField(max_length=500, description="文件存储路径")
    filesize = fields.BigIntField(description="文件大小（字节）")
    file_hash = fields.CharField(max_length=64, null=True, description="文件哈希值")
    uploader = fields.ForeignKeyField("app_system.User", related_name="upload_records", description="上传者")
    status = fields.CharEnumField(UploadStatus, default=UploadStatus.UPLOADED, description="记录状态")
    parsed_result = fields.JSONField(null=True, description="解析结果缓存")
    parse_time = fields.DatetimeField(null=True, description="解析完成时间")
    
    class Meta:
        table = "strm_upload_records_optimized"
        table_description = "优化的文件上传记录表"
        default_connection = "conn_system"
        indexes = [
            ("uploader_id",),
            ("status",),
            ("create_time",),
            ("parse_time",),
            ("file_hash",),  # 用于去重
            ("uploader_id", "status"),
            ("uploader_id", "create_time"),
            ("status", "create_time"),
            ("filename",),
        ]
    
    async def get_file_content(self) -> bytes:
        """获取文件内容"""
        if not self.file_path or not Path(self.file_path).exists():
            raise FileNotFoundError(f"文件不存在: {self.file_path}")
        
        with open(self.file_path, 'rb') as f:
            return f.read()
    
    async def save_file_content(self, content: bytes) -> str:
        """保存文件内容并返回路径"""
        # 生成存储路径
        storage_dir = Path("storage/uploads")
        date_str = datetime.now().strftime("%Y/%m/%d")
        dir_path = storage_dir / date_str
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        file_extension = Path(self.filename).suffix
        file_hash = hashlib.md5(content).hexdigest()
        filename = f"{self.id}_{file_hash[:8]}{file_extension}"
        file_path = dir_path / filename
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(content)
        
        # 更新记录
        self.file_path = str(file_path.relative_to(Path.cwd()))
        self.file_hash = file_hash
        self.filesize = len(content)
        
        return self.file_path
    '''
    
    log.info("优化的上传模型代码已生成，请参考上述代码进行模型重构")


async def main():
    """主函数"""
    log.info("🚀 开始文件存储迁移...")
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
        
        # 创建迁移实例
        migration = FileStorageMigration()
        
        # 执行迁移
        await migration.migrate_upload_records()
        
        # 验证迁移
        await migration.verify_migration()
        
        # 生成优化模型参考
        await create_optimized_upload_model()
        
        log.info("✅ 文件存储迁移完成!")
        
    except Exception as e:
        log.error(f"❌ 文件存储迁移失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
