"""
数据库优化迁移脚本
安全地添加索引和优化数据库性能
"""

import asyncio
import time
from tortoise import Tortoise, connections
from app.settings.config import APP_SETTINGS
from app.log.log import log


async def create_performance_indexes():
    """创建性能优化索引"""
    
    # 初始化数据库连接
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    conn = connections.get("conn_system")
    
    # 定义需要创建的索引
    indexes = [
        # DownloadLog表索引
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_id ON strm_download_logs(task_id);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_file_type ON strm_download_logs(file_type);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_is_success ON strm_download_logs(is_success);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_log_level ON strm_download_logs(log_level);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_create_time ON strm_download_logs(create_time);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_create_time ON strm_download_logs(task_id, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_level ON strm_download_logs(task_id, log_level);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_success ON strm_download_logs(task_id, is_success);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_file_type ON strm_download_logs(task_id, file_type);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_success_time ON strm_download_logs(task_id, is_success, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_download_logs_task_level_time ON strm_download_logs(task_id, log_level, create_time);",
        
        # StrmLog表索引
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_id ON strm_generation_logs(task_id);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_file_type ON strm_generation_logs(file_type);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_is_success ON strm_generation_logs(is_success);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_log_level ON strm_generation_logs(log_level);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_create_time ON strm_generation_logs(create_time);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_create_time ON strm_generation_logs(task_id, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_level ON strm_generation_logs(task_id, log_level);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_success ON strm_generation_logs(task_id, is_success);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_file_type ON strm_generation_logs(task_id, file_type);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_success_time ON strm_generation_logs(task_id, is_success, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_strm_logs_task_level_time ON strm_generation_logs(task_id, log_level, create_time);",
        
        # UploadRecord表索引
        "CREATE INDEX IF NOT EXISTS idx_upload_records_uploader_id ON strm_upload_records(uploader_id);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_status ON strm_upload_records(status);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_create_time ON strm_upload_records(create_time);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_parse_time ON strm_upload_records(parse_time);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_uploader_status ON strm_upload_records(uploader_id, status);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_uploader_create_time ON strm_upload_records(uploader_id, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_status_create_time ON strm_upload_records(status, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_upload_records_filename ON strm_upload_records(filename);",
        
        # 优化现有表的额外索引
        # StrmTask表额外索引
        "CREATE INDEX IF NOT EXISTS idx_strm_tasks_status_create_time ON strm_tasks(status, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_strm_tasks_server_id ON strm_tasks(server_id);",
        "CREATE INDEX IF NOT EXISTS idx_strm_tasks_download_server_id ON strm_tasks(download_server_id);",
        
        # StrmFile表额外索引
        "CREATE INDEX IF NOT EXISTS idx_strm_files_file_size ON strm_files(file_size);",
        "CREATE INDEX IF NOT EXISTS idx_strm_files_task_size ON strm_files(task_id, file_size);",
        
        # APILog表优化索引
        "CREATE INDEX IF NOT EXISTS idx_api_logs_user_id ON api_logs(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_api_logs_user_create_time ON api_logs(user_id, create_time);",
        "CREATE INDEX IF NOT EXISTS idx_api_logs_response_code_time ON api_logs(response_code, create_time);",
    ]
    
    log.info("开始创建性能优化索引...")
    
    success_count = 0
    error_count = 0
    
    for i, index_sql in enumerate(indexes, 1):
        try:
            start_time = time.time()
            await conn.execute_query(index_sql)
            end_time = time.time()
            
            # 提取索引名称用于日志
            index_name = index_sql.split("idx_")[1].split(" ")[0] if "idx_" in index_sql else f"index_{i}"
            log.info(f"✅ 索引 {index_name} 创建成功 (耗时: {end_time - start_time:.2f}s)")
            success_count += 1
            
        except Exception as e:
            log.error(f"❌ 创建索引失败: {index_sql}")
            log.error(f"错误信息: {str(e)}")
            error_count += 1
    
    log.info(f"索引创建完成: 成功 {success_count} 个, 失败 {error_count} 个")
    
    # 关闭连接
    await Tortoise.close_connections()


async def analyze_database_performance():
    """分析数据库性能"""
    
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    conn = connections.get("conn_system")
    
    log.info("开始分析数据库性能...")
    
    # 检查表大小
    tables_info = [
        "strm_tasks",
        "strm_files", 
        "strm_download_tasks",
        "strm_download_logs",
        "strm_generation_logs",
        "strm_upload_records",
        "users",
        "api_logs"
    ]
    
    for table in tables_info:
        try:
            # 获取表记录数
            count_result = await conn.execute_query(f"SELECT COUNT(*) as count FROM {table}")
            count = count_result[1][0]["count"] if count_result[1] else 0
            
            # 获取表大小信息（SQLite特定）
            size_result = await conn.execute_query(
                f"SELECT page_count * page_size as size FROM pragma_page_count('{table}'), pragma_page_size"
            )
            size = size_result[1][0]["size"] if size_result[1] else 0
            size_mb = size / (1024 * 1024)
            
            log.info(f"📊 表 {table}: {count:,} 条记录, 大小: {size_mb:.2f} MB")
            
        except Exception as e:
            log.warning(f"⚠️ 无法获取表 {table} 的信息: {str(e)}")
    
    # 检查索引使用情况
    try:
        indexes_result = await conn.execute_query(
            "SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'"
        )
        if indexes_result[1]:
            log.info(f"📈 当前自定义索引数量: {len(indexes_result[1])}")
            for index_info in indexes_result[1][:10]:  # 显示前10个
                log.info(f"   - {index_info['name']} (表: {index_info['tbl_name']})")
    except Exception as e:
        log.warning(f"⚠️ 无法获取索引信息: {str(e)}")
    
    await Tortoise.close_connections()


async def optimize_sqlite_settings():
    """优化SQLite设置"""
    
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    conn = connections.get("conn_system")
    
    log.info("开始优化SQLite设置...")
    
    # 优化设置
    optimizations = [
        "PRAGMA optimize;",  # 优化查询计划器
        "PRAGMA analysis_limit=1000;",  # 设置分析限制
        "PRAGMA cache_size=20000;",  # 增加缓存大小到80MB
        "PRAGMA temp_store=MEMORY;",  # 临时存储使用内存
    ]
    
    for optimization in optimizations:
        try:
            await conn.execute_query(optimization)
            log.info(f"✅ 执行优化: {optimization}")
        except Exception as e:
            log.error(f"❌ 优化失败: {optimization}, 错误: {str(e)}")
    
    await Tortoise.close_connections()


async def main():
    """主函数"""
    log.info("🚀 开始数据库优化...")
    
    try:
        # 分析当前性能
        await analyze_database_performance()
        
        # 创建索引
        await create_performance_indexes()
        
        # 优化设置
        await optimize_sqlite_settings()
        
        # 再次分析性能
        log.info("优化后性能分析:")
        await analyze_database_performance()
        
        log.info("✅ 数据库优化完成!")
        
    except Exception as e:
        log.error(f"❌ 数据库优化失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
